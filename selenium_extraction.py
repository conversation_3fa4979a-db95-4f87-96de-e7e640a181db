#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium无头浏览器提取发布时间
解决反爬虫和JavaScript渲染问题
"""

import pandas as pd
import re
from datetime import datetime
import time
import json
from pathlib import Path
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import requests
import urllib3
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SeleniumPublishTimeExtractor:
    def __init__(self):
        self.setup_driver()
        self.delay = 0.5  # 减少延迟时间
        
        # 时间格式模式
        self.time_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{1,2}\s+\w+\s+\d{4}',
            r'\w+\s+\d{1,2},\s+\d{4}',
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{2}\.\d{2}\.\d{4}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
        ]
        
        # 时间选择器
        self.time_selectors = [
            'time[datetime]',
            '[datetime]',
            '.publish-time', '.publication-date', '.article-date', '.date', '.timestamp',
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]', 'meta[name="date"]', 'meta[name="DC.date.issued"]',
            '.entry-date', '.post-date', '.created-date', '.updated-date',
            '[class*="date"]', '[id*="date"]', '[class*="time"]', '[id*="time"]',
            '.byline', '.author-date', '.article-meta',
            'span[data-date]', 'div[data-date]', 'p[data-date]',
        ]
        
        # 跳过的域名
        self.skip_domains = {
            'www.facebook.com',  # Facebook需要登录
            'facebook.com',
            'x.com',  # X(Twitter)需要登录
            'twitter.com',
        }

    def setup_driver(self):
        """设置Chrome无头浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')

            # 使用webdriver-manager自动管理Chrome驱动
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(15)  # 减少页面加载超时时间
            self.driver.implicitly_wait(5)  # 设置隐式等待
            logger.info("Chrome无头浏览器初始化成功")

        except Exception as e:
            logger.error(f"无法初始化Chrome浏览器: {str(e)}")
            logger.info("尝试使用requests作为备用方案")
            self.driver = None

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL并正确分割多个URL"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        # 处理多种分隔符：分号、换行、中文分号
        separators = [';', '；', '\n']
        
        # 先按分隔符分割
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # 检查是否是格式错误的URL（用"-"替代"/"）
            if re.match(r'^https?://[^/\s]+-', part) or re.match(r'^[^/\s]+-.*', part):
                # 修复URL格式
                if not part.startswith('http'):
                    part = 'https://' + part
                
                # 将"-"替换为"/"，但保留域名中的"-"
                url_parts = part.split('://', 1)
                if len(url_parts) == 2:
                    protocol, rest = url_parts
                    if '/' in rest:
                        domain, path = rest.split('/', 1)
                        fixed_url = f"{protocol}://{domain}/{path}"
                    else:
                        # 只有域名部分，需要处理路径
                        domain_parts = rest.split('-')
                        if len(domain_parts) > 1 and '.' in domain_parts[0]:
                            domain = domain_parts[0]
                            path_parts = domain_parts[1:]
                            fixed_url = f"{protocol}://{domain}/" + "/".join(path_parts)
                        else:
                            fixed_url = part
                    urls.append(fixed_url)
                else:
                    urls.append(part)
            elif part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part and not part.startswith('http'):
                # 可能是缺少协议的URL
                urls.append('https://' + part)
        
        # 去重并过滤有效URL
        valid_urls = []
        for url in set(urls):
            try:
                from urllib.parse import urlparse
                parsed = urlparse(url)
                if parsed.netloc and parsed.scheme in ['http', 'https']:
                    valid_urls.append(url)
            except:
                continue
        
        return valid_urls

    def should_skip_domain(self, url):
        """检查是否应该跳过该域名"""
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc.lower()
            return any(skip_domain in domain for skip_domain in self.skip_domains)
        except:
            return False

    def extract_time_with_selenium(self, url):
        """使用Selenium提取时间"""
        if not self.driver:
            return None

        try:
            logger.info(f"使用Selenium访问: {url}")
            self.driver.get(url)

            # 减少等待时间，优先检查快速可获取的元素
            time.sleep(1)
            
            # 方法1: 尝试从特定元素提取时间
            for selector in self.time_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        # 检查各种属性
                        for attr in ['datetime', 'content', 'data-date', 'data-time']:
                            try:
                                attr_value = element.get_attribute(attr)
                                if attr_value:
                                    parsed_time = self.parse_time_string(attr_value)
                                    if parsed_time:
                                        return parsed_time
                            except:
                                continue
                        
                        # 检查元素文本
                        try:
                            text = element.text.strip()
                            if text:
                                parsed_time = self.parse_time_string(text)
                                if parsed_time:
                                    return parsed_time
                        except:
                            continue
                except:
                    continue
            
            # 方法2: 检查页面源码中的JSON-LD
            try:
                scripts = self.driver.find_elements(By.CSS_SELECTOR, 'script[type="application/ld+json"]')
                for script in scripts:
                    try:
                        script_content = script.get_attribute('innerHTML')
                        if script_content:
                            data = json.loads(script_content)
                            if isinstance(data, dict):
                                for key in ['datePublished', 'publishedDate', 'dateCreated']:
                                    if key in data:
                                        parsed_time = self.parse_time_string(data[key])
                                        if parsed_time:
                                            return parsed_time
                    except:
                        continue
            except:
                pass
            
            # 方法3: 在页面文本中搜索时间模式
            try:
                page_text = self.driver.page_source
                for pattern in self.time_patterns:
                    matches = re.findall(pattern, page_text)
                    for match in matches:
                        parsed_time = self.parse_time_string(match)
                        if parsed_time:
                            # 验证时间合理性
                            try:
                                dt = datetime.strptime(parsed_time.split()[0], '%Y-%m-%d')
                                current_year = datetime.now().year
                                if 2000 <= dt.year <= current_year + 1:
                                    return parsed_time
                            except:
                                continue
            except:
                pass
            
            # 方法4: 从URL提取日期
            url_date = self.extract_date_from_url(url)
            if url_date:
                return url_date
            
            return None
            
        except TimeoutException:
            logger.error(f"页面加载超时: {url}")
            return None
        except WebDriverException as e:
            logger.error(f"WebDriver错误 {url}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Selenium提取时间出错 {url}: {str(e)}")
            return None

    def extract_time_with_requests(self, url):
        """使用requests作为备用方案"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
                'Connection': 'keep-alive',
            }
            
            response = requests.get(url, headers=headers, timeout=8, verify=False)  # 减少超时时间
            response.raise_for_status()
            
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding
            
            # 简单的时间提取
            for pattern in self.time_patterns:
                matches = re.findall(pattern, response.text)
                for match in matches:
                    parsed_time = self.parse_time_string(match)
                    if parsed_time:
                        try:
                            dt = datetime.strptime(parsed_time.split()[0], '%Y-%m-%d')
                            current_year = datetime.now().year
                            if 2000 <= dt.year <= current_year + 1:
                                return parsed_time
                        except:
                            continue
            
            return None
            
        except Exception as e:
            logger.error(f"Requests备用方案失败 {url}: {str(e)}")
            return None

    def extract_date_from_url(self, url):
        """从URL路径中提取日期"""
        try:
            url_patterns = [
                r'/(\d{4})/(\d{2})/(\d{2})/',
                r'/(\d{4})-(\d{2})-(\d{2})/',
                r'/(\d{4})(\d{2})(\d{2})/',
                r'(\d{4})/(\d{2})/(\d{2})',
                r'(\d{4})-(\d{2})-(\d{2})',
            ]
            
            for pattern in url_patterns:
                match = re.search(pattern, url)
                if match:
                    year, month, day = match.groups()
                    try:
                        dt = datetime(int(year), int(month), int(day))
                        if 2000 <= dt.year <= datetime.now().year + 1:
                            return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def parse_time_string(self, time_str):
        """解析时间字符串"""
        if not time_str:
            return None
        
        time_str = str(time_str).strip()
        time_str = re.sub(r'[^\w\s:/-]', ' ', time_str)
        time_str = re.sub(r'\s+', ' ', time_str).strip()
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y.%m.%d',
            '%d.%m.%Y',
            '%d %B %Y',
            '%B %d %Y',
            '%d %b %Y',
            '%b %d %Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        
        return None

    def fetch_publish_time_from_url(self, url):
        """从URL获取发布时间 - 优化速度版本"""
        if self.should_skip_domain(url):
            logger.warning(f"跳过社交媒体网站: {url}")
            return None

        # 先尝试从URL路径提取日期（最快）
        url_date = self.extract_date_from_url(url)
        if url_date:
            logger.info(f"从URL提取时间: {url_date}")
            return url_date

        # 优先使用requests（更快）
        result = self.extract_time_with_requests(url)
        if result:
            logger.info(f"Requests成功提取时间: {result}")
            return result

        # 最后使用Selenium（较慢但更全面）
        if self.driver:
            result = self.extract_time_with_selenium(url)
            if result:
                logger.info(f"Selenium成功提取时间: {result}")
                return result

        logger.warning(f"无法提取时间: {url}")
        return None

    def process_data_sources(self, data_sources_text):
        """处理data_sources列"""
        urls = self.fix_malformed_urls(data_sources_text)
        
        if not urls:
            logger.warning(f"无法提取有效URL: {data_sources_text}")
            return "无法获取"
        
        logger.info(f"提取到 {len(urls)} 个URL")
        
        for i, url in enumerate(urls):
            logger.info(f"尝试URL {i+1}/{len(urls)}: {url}")
            
            publish_time = self.fetch_publish_time_from_url(url)
            if publish_time:
                return publish_time
            
            time.sleep(self.delay)
        
        return "无法获取"

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器已关闭")
            except:
                pass

def process_single_row(args):
    """处理单行数据的函数，用于并行处理"""
    index, row = args

    # 为每个线程创建独立的提取器（不使用Selenium以避免冲突）
    temp_extractor = SimpleExtractor()

    logger.info(f"处理第 {index + 1} 行 (ID: {row.get('id', 'N/A')})")

    publish_time = temp_extractor.process_data_sources(row['data_sources'])

    return index, publish_time

class SimpleExtractor:
    """简化的提取器，只使用requests，用于并行处理"""
    def __init__(self):
        self.time_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
        ]

    def fix_malformed_urls(self, data_sources_text):
        """修复URL格式"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []

        text = str(data_sources_text)
        urls = []

        separators = [';', '；', '\n']
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts

        for part in parts:
            part = part.strip()
            if not part:
                continue

            if part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part:
                urls.append('https://' + part)

        return list(set(urls))

    def extract_date_from_url(self, url):
        """从URL提取日期"""
        patterns = [
            r'/(\d{4})/(\d{2})/(\d{2})/',
            r'/(\d{4})-(\d{2})-(\d{2})/',
            r'(\d{4})/(\d{2})/(\d{2})',
            r'(\d{4})-(\d{2})-(\d{2})',
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                year, month, day = match.groups()
                try:
                    dt = datetime(int(year), int(month), int(day))
                    if 2000 <= dt.year <= datetime.now().year + 1:
                        return dt.strftime('%Y-%m-%d 00:00:00')
                except:
                    continue
        return None

    def parse_time_string(self, time_str):
        """解析时间字符串"""
        if not time_str:
            return None

        time_str = str(time_str).strip()
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y']

        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        return None

    def extract_time_fast(self, url):
        """快速提取时间"""
        # 先从URL提取
        url_date = self.extract_date_from_url(url)
        if url_date:
            return url_date

        # 快速requests请求
        try:
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(url, headers=headers, timeout=5, verify=False)

            for pattern in self.time_patterns:
                matches = re.findall(pattern, response.text)
                for match in matches:
                    parsed = self.parse_time_string(match)
                    if parsed:
                        return parsed
        except:
            pass

        return None

    def process_data_sources(self, data_sources_text):
        """处理数据源"""
        urls = self.fix_malformed_urls(data_sources_text)

        if not urls:
            return "无法获取"

        for url in urls[:2]:  # 只检查前2个URL以提高速度
            result = self.extract_time_fast(url)
            if result:
                return result

        return "无法获取"

def process_failed_csv_file(file_path, output_folder):
    """处理失败记录的CSV文件 - 优化速度版本"""
    logger.info(f"开始处理文件: {file_path}")

    df = pd.read_csv(file_path, encoding='utf-8')

    if 'data_sources' not in df.columns or 'publish_time' not in df.columns:
        logger.error(f"文件 {file_path} 缺少必要的列")
        return 0, 0

    success_count = 0
    fail_count = 0

    # 使用并行处理加速
    logger.info(f"使用并行处理加速提取，共 {len(df)} 条记录")

    # 准备参数
    args_list = [(index, row) for index, row in df.iterrows()]

    # 并行处理
    with ThreadPoolExecutor(max_workers=4) as executor:
        future_to_index = {executor.submit(process_single_row, args): args[0] for args in args_list}

        for future in as_completed(future_to_index):
            index, publish_time = future.result()

            df.at[index, 'publish_time'] = publish_time

            if publish_time != "无法获取":
                success_count += 1
                logger.info(f"第 {index + 1} 行成功提取时间: {publish_time}")
            else:
                fail_count += 1
                logger.warning(f"第 {index + 1} 行无法提取时间")

    # 保存结果
    output_file = Path(output_folder) / f"fast_{Path(file_path).name}"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    logger.info(f"结果已保存到: {output_file}")

    return success_count, fail_count

def main():
    """主函数"""
    failed_folder = "failed_records"
    output_folder = "selenium_results"
    
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    extractor = SeleniumPublishTimeExtractor()
    
    try:
        # 只处理一个文件作为测试
        test_file = "东海_extracted_failed.csv"
        file_path = Path(failed_folder) / test_file
        
        if file_path.exists():
            success, fail = process_failed_csv_file(file_path, output_folder)
            logger.info(f"测试完成: 成功 {success} 条，失败 {fail} 条")
        else:
            logger.error(f"测试文件不存在: {file_path}")

    finally:
        if 'extractor' in locals():
            extractor.close()

if __name__ == "__main__":
    main()
