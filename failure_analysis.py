#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败原因分析脚本
分析提取失败的具体原因并提供解决方案
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import json
from pathlib import Path
from urllib.parse import urlparse
import logging
from collections import defaultdict, Counter
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FailureAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        
        self.failure_reasons = defaultdict(list)
        self.domain_failures = Counter()
        self.url_patterns = Counter()
        
        # 常见的反爬虫响应特征
        self.anti_bot_indicators = [
            'cloudflare', 'captcha', 'robot', 'bot', 'verification',
            'access denied', 'forbidden', 'blocked', 'security check'
        ]

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        # 处理多种分隔符
        separators = [';', '；', '\n', '|']
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # 修复格式错误的URL
            if re.match(r'^https?://[^/\s]+-', part):
                url_parts = part.split('://', 1)
                if len(url_parts) == 2:
                    protocol, rest = url_parts
                    if '/' not in rest and '-' in rest:
                        domain_parts = rest.split('-')
                        if len(domain_parts) > 1 and '.' in domain_parts[0]:
                            domain = domain_parts[0]
                            path_parts = domain_parts[1:]
                            fixed_url = f"{protocol}://{domain}/" + "/".join(path_parts)
                            urls.append(fixed_url)
                        else:
                            urls.append(part)
                    else:
                        urls.append(part)
                else:
                    urls.append(part)
            elif part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part and not part.startswith('http'):
                urls.append('https://' + part)
        
        return list(set(urls))

    def analyze_url_failure(self, url, row_id):
        """分析单个URL的失败原因"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            logger.info(f"分析URL: {url} (ID: {row_id})")
            
            # 检查URL格式
            if not parsed.scheme or not parsed.netloc:
                reason = "URL格式错误"
                self.failure_reasons[reason].append((row_id, url))
                return reason
            
            # 记录域名
            self.domain_failures[domain] += 1
            
            # 记录URL模式
            path_pattern = re.sub(r'\d+', 'NUM', parsed.path)
            self.url_patterns[f"{domain}{path_pattern}"] += 1
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
                'Connection': 'keep-alive',
            }
            
            try:
                response = self.session.get(url, timeout=10, headers=headers)
                
                # 分析HTTP状态码
                if response.status_code == 403:
                    reason = "403禁止访问 - 可能的反爬虫机制"
                    self.failure_reasons[reason].append((row_id, url))
                    
                    # 检查响应内容是否包含反爬虫指示器
                    content_lower = response.text.lower()
                    for indicator in self.anti_bot_indicators:
                        if indicator in content_lower:
                            reason += f" (检测到: {indicator})"
                            break
                    
                    return reason
                
                elif response.status_code == 404:
                    reason = "404页面不存在"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
                
                elif response.status_code == 405:
                    reason = "405方法不允许"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
                
                elif response.status_code == 429:
                    reason = "429请求过多 - 被限流"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
                
                elif response.status_code >= 500:
                    reason = f"{response.status_code}服务器错误"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
                
                elif response.status_code == 200:
                    # 页面正常加载，分析为什么没有提取到时间
                    return self.analyze_content_failure(response.text, url, row_id)
                
                else:
                    reason = f"HTTP {response.status_code}"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
                    
            except requests.exceptions.SSLError as e:
                reason = "SSL证书错误"
                self.failure_reasons[reason].append((row_id, url))
                return reason
                
            except requests.exceptions.Timeout:
                reason = "请求超时"
                self.failure_reasons[reason].append((row_id, url))
                return reason
                
            except requests.exceptions.ConnectionError as e:
                if "certificate verify failed" in str(e).lower():
                    reason = "SSL证书验证失败"
                else:
                    reason = "连接错误"
                self.failure_reasons[reason].append((row_id, url))
                return reason
                
            except Exception as e:
                reason = f"请求异常: {type(e).__name__}"
                self.failure_reasons[reason].append((row_id, url))
                return reason
                
        except Exception as e:
            reason = f"分析异常: {type(e).__name__}"
            self.failure_reasons[reason].append((row_id, url))
            return reason

    def analyze_content_failure(self, html_content, url, row_id):
        """分析内容提取失败的原因"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 检查是否是JavaScript重定向或需要JS渲染
            if "javascript" in html_content.lower() and len(soup.get_text().strip()) < 100:
                reason = "需要JavaScript渲染"
                self.failure_reasons[reason].append((row_id, url))
                return reason
            
            # 检查是否包含反爬虫页面
            content_lower = html_content.lower()
            for indicator in self.anti_bot_indicators:
                if indicator in content_lower:
                    reason = f"反爬虫页面 (检测到: {indicator})"
                    self.failure_reasons[reason].append((row_id, url))
                    return reason
            
            # 检查页面是否包含时间相关元素
            time_elements = soup.find_all(['time', 'meta'])
            date_classes = soup.find_all(attrs={'class': re.compile(r'date|time|publish', re.I)})
            
            if not time_elements and not date_classes:
                reason = "页面无时间元素"
                self.failure_reasons[reason].append((row_id, url))
                return reason
            
            # 检查是否有时间元素但格式不匹配
            time_patterns = [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{4}/\d{2}/\d{2}',
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\w+\s+\d{1,2},\s+\d{4}',
                r'\d{4}年\d{1,2}月\d{1,2}日',
            ]
            
            has_time_pattern = False
            for pattern in time_patterns:
                if re.search(pattern, html_content):
                    has_time_pattern = True
                    break
            
            if has_time_pattern:
                reason = "有时间信息但提取失败 - 格式不匹配"
                self.failure_reasons[reason].append((row_id, url))
                return reason
            else:
                reason = "页面无可识别的时间格式"
                self.failure_reasons[reason].append((row_id, url))
                return reason
                
        except Exception as e:
            reason = f"内容分析异常: {type(e).__name__}"
            self.failure_reasons[reason].append((row_id, url))
            return reason

    def analyze_failed_records(self, file_path):
        """分析失败记录文件"""
        logger.info(f"开始分析失败记录: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8')
        
        if 'data_sources' not in df.columns:
            logger.error(f"文件 {file_path} 缺少data_sources列")
            return
        
        total_records = len(df)
        logger.info(f"总记录数: {total_records}")
        
        for index, row in df.iterrows():
            row_id = row.get('id', f'row_{index}')
            data_sources = row['data_sources']
            
            urls = self.fix_malformed_urls(data_sources)
            
            if not urls:
                self.failure_reasons["无有效URL"].append((row_id, data_sources))
                continue
            
            # 分析每个URL
            for url in urls:
                failure_reason = self.analyze_url_failure(url, row_id)
                logger.info(f"ID {row_id}: {failure_reason}")

    def generate_report(self, output_file):
        """生成分析报告"""
        logger.info("生成失败原因分析报告...")
        
        report = []
        report.append("=" * 60)
        report.append("失败原因分析报告")
        report.append("=" * 60)
        report.append("")
        
        # 失败原因统计
        report.append("1. 失败原因统计:")
        report.append("-" * 30)
        for reason, records in self.failure_reasons.items():
            report.append(f"{reason}: {len(records)} 条")
            # 显示前3个示例
            for i, (row_id, url) in enumerate(records[:3]):
                report.append(f"  - ID {row_id}: {url}")
            if len(records) > 3:
                report.append(f"  ... 还有 {len(records) - 3} 条")
            report.append("")
        
        # 域名失败统计
        report.append("2. 域名失败统计 (Top 10):")
        report.append("-" * 30)
        for domain, count in self.domain_failures.most_common(10):
            report.append(f"{domain}: {count} 次失败")
        report.append("")
        
        # URL模式统计
        report.append("3. URL模式统计 (Top 10):")
        report.append("-" * 30)
        for pattern, count in self.url_patterns.most_common(10):
            report.append(f"{pattern}: {count} 次")
        report.append("")
        
        # 解决方案建议
        report.append("4. 解决方案建议:")
        report.append("-" * 30)
        
        if "403禁止访问" in [r for r in self.failure_reasons.keys() if "403" in r]:
            report.append("• 403错误解决方案:")
            report.append("  - 使用代理IP池")
            report.append("  - 增加请求间隔")
            report.append("  - 使用更真实的浏览器指纹")
            report.append("  - 尝试不同的User-Agent")
            report.append("")
        
        if any("JavaScript" in r for r in self.failure_reasons.keys()):
            report.append("• JavaScript渲染问题:")
            report.append("  - 使用Selenium或Playwright")
            report.append("  - 增加页面加载等待时间")
            report.append("")
        
        if any("SSL" in r for r in self.failure_reasons.keys()):
            report.append("• SSL证书问题:")
            report.append("  - 禁用SSL验证")
            report.append("  - 更新证书库")
            report.append("")
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        logger.info(f"分析报告已保存到: {output_file}")
        
        # 同时打印到控制台
        print('\n'.join(report))

def main():
    """主函数"""
    failed_folder = "failed_records"
    
    analyzer = FailureAnalyzer()
    
    # 分析东海失败记录
    test_file = "东海_extracted_failed.csv"
    file_path = Path(failed_folder) / test_file
    
    if file_path.exists():
        analyzer.analyze_failed_records(file_path)
        
        # 生成报告
        report_file = "failure_analysis_report.txt"
        analyzer.generate_report(report_file)
    else:
        logger.error(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
