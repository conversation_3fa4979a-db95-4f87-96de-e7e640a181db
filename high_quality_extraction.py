#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量时间提取脚本
结合多种策略提高成功率
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import time
import json
from pathlib import Path
from urllib.parse import urlparse, urljoin
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HighQualityExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        
        # 多种User-Agent轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
        ]
        self.ua_index = 0
        
        # 扩展的时间模式
        self.time_patterns = [
            # 标准格式
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            # 美式格式
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{2}/\d{2}/\d{4}',
            # 英文月份格式
            r'\w+\s+\d{1,2},\s+\d{4}',
            r'\d{1,2}\s+\w+\s+\d{4}',
            # 中文格式
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{1,2}月\d{1,2}日',
            # ISO格式
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z',
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z',
            # 点分格式
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{2}\.\d{2}\.\d{4}',
        ]
        
        # 扩展的时间选择器
        self.time_selectors = [
            # 标准时间属性
            'time[datetime]', '[datetime]', 'time[pubdate]',
            # Meta标签
            'meta[property="article:published_time"]',
            'meta[property="article:modified_time"]',
            'meta[name="publishdate"]', 'meta[name="date"]',
            'meta[name="DC.date.issued"]', 'meta[name="DC.date.created"]',
            'meta[name="article:published_time"]',
            'meta[name="sailthru.date"]',
            'meta[itemprop="datePublished"]',
            'meta[itemprop="dateCreated"]',
            # 常见CSS类
            '.publish-time', '.publication-date', '.article-date', 
            '.date', '.timestamp', '.entry-date', '.post-date',
            '.created-date', '.updated-date', '.byline',
            '.author-date', '.article-meta', '.news-date',
            '.story-date', '.content-date',
            # 通配符选择器
            '[class*="date"]', '[id*="date"]', 
            '[class*="time"]', '[id*="time"]',
            '[class*="publish"]', '[id*="publish"]',
            # 数据属性
            '[data-date]', '[data-time]', '[data-published]',
            '[data-timestamp]', '[data-publish-date]',
        ]

    def get_next_user_agent(self):
        """获取下一个User-Agent"""
        ua = self.user_agents[self.ua_index]
        self.ua_index = (self.ua_index + 1) % len(self.user_agents)
        return ua

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL并正确分割"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        # 处理多种分隔符
        separators = [';', '；', '\n', '|']
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # 修复格式错误的URL
            if re.match(r'^https?://[^/\s]+-', part):
                # 处理用"-"替代"/"的情况
                url_parts = part.split('://', 1)
                if len(url_parts) == 2:
                    protocol, rest = url_parts
                    if '/' not in rest and '-' in rest:
                        # 可能需要修复路径
                        domain_parts = rest.split('-')
                        if len(domain_parts) > 1 and '.' in domain_parts[0]:
                            domain = domain_parts[0]
                            path_parts = domain_parts[1:]
                            fixed_url = f"{protocol}://{domain}/" + "/".join(path_parts)
                            urls.append(fixed_url)
                        else:
                            urls.append(part)
                    else:
                        urls.append(part)
                else:
                    urls.append(part)
            elif part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part and not part.startswith('http'):
                urls.append('https://' + part)
        
        # 去重并验证
        valid_urls = []
        for url in set(urls):
            try:
                parsed = urlparse(url)
                if parsed.netloc and parsed.scheme in ['http', 'https']:
                    valid_urls.append(url)
            except:
                continue
        
        return valid_urls

    def extract_date_from_url(self, url):
        """从URL路径中提取日期"""
        try:
            patterns = [
                r'/(\d{4})/(\d{2})/(\d{2})/',
                r'/(\d{4})-(\d{2})-(\d{2})/',
                r'/(\d{4})(\d{2})(\d{2})/',
                r'(\d{4})/(\d{2})/(\d{2})',
                r'(\d{4})-(\d{2})-(\d{2})',
                r'/(\d{4})/(\d{1,2})/(\d{1,2})/',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    year, month, day = match.groups()
                    try:
                        # 补零
                        month = month.zfill(2)
                        day = day.zfill(2)
                        dt = datetime(int(year), int(month), int(day))
                        if 2000 <= dt.year <= datetime.now().year + 1:
                            return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def parse_time_string(self, time_str):
        """解析时间字符串为标准格式"""
        if not time_str:
            return None
        
        time_str = str(time_str).strip()
        
        # 清理字符串
        time_str = re.sub(r'[^\w\s:/-]', ' ', time_str)
        time_str = re.sub(r'\s+', ' ', time_str).strip()
        
        # 扩展的时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y.%m.%d',
            '%d.%m.%Y',
            '%d %B %Y',
            '%B %d %Y',
            '%d %b %Y',
            '%b %d %Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%a %b %d %H:%M:%S %Y',
            '%a, %d %b %Y %H:%M:%S',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        
        # 尝试提取时间部分
        for pattern in self.time_patterns:
            match = re.search(pattern, time_str)
            if match:
                matched_str = match.group()
                for fmt in formats:
                    try:
                        dt = datetime.strptime(matched_str, fmt)
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        continue
        
        return None

    def extract_time_from_html(self, html_content, url):
        """从HTML内容中提取发布时间"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 方法1: 检查特定的HTML元素
            for selector in self.time_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        # 检查各种属性
                        for attr in ['datetime', 'content', 'data-date', 'data-time', 'data-published', 'data-timestamp']:
                            if element.get(attr):
                                time_str = element.get(attr)
                                parsed_time = self.parse_time_string(time_str)
                                if parsed_time:
                                    return parsed_time
                        
                        # 检查元素文本
                        if element.text:
                            parsed_time = self.parse_time_string(element.text.strip())
                            if parsed_time:
                                return parsed_time
                except:
                    continue
            
            # 方法2: 检查JSON-LD结构化数据
            json_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_scripts:
                try:
                    if script.string:
                        data = json.loads(script.string)
                        if isinstance(data, dict):
                            for key in ['datePublished', 'publishedDate', 'dateCreated', 'dateModified']:
                                if key in data:
                                    parsed_time = self.parse_time_string(data[key])
                                    if parsed_time:
                                        return parsed_time
                        elif isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    for key in ['datePublished', 'publishedDate', 'dateCreated']:
                                        if key in item:
                                            parsed_time = self.parse_time_string(item[key])
                                            if parsed_time:
                                                return parsed_time
                except:
                    continue
            
            # 方法3: 在页面文本中搜索时间模式
            page_text = soup.get_text()
            for pattern in self.time_patterns:
                matches = re.findall(pattern, page_text)
                for match in matches:
                    parsed_time = self.parse_time_string(match)
                    if parsed_time:
                        # 验证时间合理性
                        try:
                            dt = datetime.strptime(parsed_time.split()[0], '%Y-%m-%d')
                            current_year = datetime.now().year
                            if 2000 <= dt.year <= current_year + 1:
                                return parsed_time
                        except:
                            continue
            
            return None
            
        except Exception as e:
            logger.error(f"解析HTML时出错 {url}: {str(e)}")
            return None

    def fetch_publish_time_from_url(self, url):
        """从单个URL获取发布时间"""
        try:
            # 先尝试从URL提取日期
            url_date = self.extract_date_from_url(url)
            if url_date:
                logger.info(f"从URL提取时间: {url_date}")
                return url_date
            
            logger.info(f"正在访问: {url}")
            
            headers = {
                'User-Agent': self.get_next_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0',
            }
            
            response = self.session.get(url, timeout=10, headers=headers)
            response.raise_for_status()
            
            # 检测编码
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding
            
            publish_time = self.extract_time_from_html(response.text, url)
            
            if publish_time:
                logger.info(f"成功提取时间: {publish_time}")
                return publish_time
            else:
                logger.warning(f"未能提取到时间信息: {url}")
                return None
                
        except Exception as e:
            logger.error(f"处理URL时出错 {url}: {str(e)}")
            return None

    def process_data_sources(self, data_sources_text):
        """处理data_sources列，提取发布时间"""
        urls = self.fix_malformed_urls(data_sources_text)
        
        if not urls:
            logger.warning(f"无法提取有效URL: {data_sources_text}")
            return "无法获取"
        
        logger.info(f"提取到 {len(urls)} 个URL")
        
        # 尝试从每个URL提取时间
        for i, url in enumerate(urls):
            logger.info(f"尝试URL {i+1}/{len(urls)}: {url}")
            
            publish_time = self.fetch_publish_time_from_url(url)
            if publish_time:
                return publish_time
            
            # 短暂延迟
            time.sleep(0.5)
        
        return "无法获取"

def process_single_row_hq(args):
    """高质量处理单行数据"""
    index, row = args
    
    extractor = HighQualityExtractor()
    
    logger.info(f"处理第 {index + 1} 行 (ID: {row.get('id', 'N/A')})")
    
    publish_time = extractor.process_data_sources(row['data_sources'])
    
    return index, publish_time

def process_failed_csv_file_hq(file_path, output_folder):
    """高质量处理失败记录的CSV文件"""
    logger.info(f"开始高质量处理文件: {file_path}")
    
    df = pd.read_csv(file_path, encoding='utf-8')
    
    if 'data_sources' not in df.columns or 'publish_time' not in df.columns:
        logger.error(f"文件 {file_path} 缺少必要的列")
        return 0, 0
    
    success_count = 0
    fail_count = 0
    
    # 使用较少的并行线程以提高质量
    logger.info(f"使用高质量模式处理，共 {len(df)} 条记录")
    
    args_list = [(index, row) for index, row in df.iterrows()]
    
    with ThreadPoolExecutor(max_workers=2) as executor:  # 减少并发数
        future_to_index = {executor.submit(process_single_row_hq, args): args[0] for args in args_list}
        
        for future in as_completed(future_to_index):
            index, publish_time = future.result()
            
            df.at[index, 'publish_time'] = publish_time
            
            if publish_time != "无法获取":
                success_count += 1
                logger.info(f"第 {index + 1} 行成功提取时间: {publish_time}")
            else:
                fail_count += 1
                logger.warning(f"第 {index + 1} 行无法提取时间")
    
    # 保存结果
    output_file = Path(output_folder) / f"hq_{Path(file_path).name}"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    logger.info(f"结果已保存到: {output_file}")
    
    return success_count, fail_count

def main():
    """主函数"""
    failed_folder = "failed_records"
    output_folder = "hq_results"
    
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    # 处理一个文件作为测试
    test_file = "东海_extracted_failed.csv"
    file_path = Path(failed_folder) / test_file
    
    if file_path.exists():
        success, fail = process_failed_csv_file_hq(file_path, output_folder)
        logger.info(f"高质量处理完成: 成功 {success} 条，失败 {fail} 条")
        if success + fail > 0:
            success_rate = (success / (success + fail)) * 100
            logger.info(f"成功率: {success_rate:.1f}%")
    else:
        logger.error(f"测试文件不存在: {file_path}")

if __name__ == "__main__":
    main()
