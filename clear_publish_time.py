#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空extracted_data文件夹中所有CSV文件的publish_time列内容
"""

import pandas as pd
import os
from pathlib import Path

def clear_publish_time_column(folder_path):
    """
    清空指定文件夹中所有CSV文件的publish_time列内容
    
    Args:
        folder_path (str): 文件夹路径
    """
    
    folder = Path(folder_path)
    if not folder.exists():
        print(f"文件夹 {folder_path} 不存在")
        return
    
    # 获取文件夹中的所有CSV文件
    csv_files = list(folder.glob('*.csv'))
    
    if not csv_files:
        print(f"在 {folder_path} 文件夹中没有找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    print("-" * 50)
    
    # 处理每个CSV文件
    for csv_file in csv_files:
        print(f"正在处理文件: {csv_file.name}")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 检查是否包含publish_time列
            if 'publish_time' not in df.columns:
                print(f"  警告: 文件 {csv_file.name} 中没有找到 publish_time 列")
                continue
            
            # 显示原始数据统计
            original_non_empty = df['publish_time'].notna().sum()
            print(f"  原始数据: {len(df)} 行，其中 {original_non_empty} 行有publish_time数据")
            
            # 清空publish_time列的内容
            df['publish_time'] = ''  # 设置为空字符串
            
            # 保存修改后的文件
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 验证修改结果
            df_check = pd.read_csv(csv_file, encoding='utf-8')
            remaining_non_empty = df_check['publish_time'].notna().sum()
            empty_count = (df_check['publish_time'] == '').sum()
            
            print(f"  修改后: {len(df_check)} 行，{empty_count} 行publish_time为空")
            print(f"  ✓ 成功清空 {csv_file.name} 的publish_time列")
            
        except Exception as e:
            print(f"  ✗ 处理文件 {csv_file.name} 时出错: {str(e)}")
            continue
        
        print()
    
    print("-" * 50)
    print("所有文件处理完成！")

def main():
    """主函数"""
    folder_path = "extracted_data"
    
    print("开始清空CSV文件中的publish_time列内容...")
    print(f"目标文件夹: {folder_path}")
    print("-" * 50)
    
    # 执行清空操作
    clear_publish_time_column(folder_path)

if __name__ == "__main__":
    main()
