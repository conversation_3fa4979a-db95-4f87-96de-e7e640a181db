#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本：重新尝试提取失败记录的发布时间
针对URL格式问题和其他失败原因进行优化处理
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import time
import json
from pathlib import Path
from urllib.parse import urlparse, urljoin
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedPublishTimeExtractor:
    def __init__(self):
        self.session = requests.Session()

        # 设置重试策略
        retry_strategy = Retry(
            total=2,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 禁用SSL验证以避免SSL错误
        self.session.verify = False
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 更全面的User-Agent轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]
        self.current_ua_index = 0

        self.timeout = 20
        self.delay = 3  # 增加请求间隔

        # 跳过的域名列表（已知会阻止访问的）
        self.blocked_domains = {
            'www.usfj.mil',
            'www.pacom.mil',
            'www.pacaf.af.mil',
            'www.navy.mil',
            'www.c7f.navy.mil'
        }
        
        # 扩展的时间格式模式
        self.time_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{1,2}\s+\w+\s+\d{4}',
            r'\w+\s+\d{1,2},\s+\d{4}',
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{2}\.\d{2}\.\d{4}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{1,2}月\d{1,2}日',
        ]
        
        # 扩展的时间选择器
        self.time_selectors = [
            'time[datetime]',
            '[datetime]',
            '.publish-time', '.publication-date', '.article-date', '.date', '.timestamp',
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]', 'meta[name="date"]', 'meta[name="DC.date.issued"]',
            '.entry-date', '.post-date', '.created-date', '.updated-date',
            '[class*="date"]', '[id*="date"]', '[class*="time"]', '[id*="time"]',
            '.byline', '.author-date', '.article-meta',
            'span[data-date]', 'div[data-date]', 'p[data-date]',
        ]

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        # 处理用"-"替代"/"的URL格式问题
        # 例如: www.pacom.mil-Media-News -> www.pacom.mil/Media/News
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是格式错误的URL
            if re.match(r'^https?://[^/\s]+-', line) or re.match(r'^[^/\s]+-.*', line):
                # 修复URL格式
                if not line.startswith('http'):
                    line = 'https://' + line
                
                # 将"-"替换为"/"，但保留域名中的"-"
                parts = line.split('://', 1)
                if len(parts) == 2:
                    protocol, rest = parts
                    domain_and_path = rest.split('/', 1)
                    if len(domain_and_path) == 1:
                        # 只有域名部分，需要处理路径
                        domain_parts = domain_and_path[0].split('-')
                        if len(domain_parts) > 1:
                            # 假设第一部分是域名，其余是路径
                            domain = domain_parts[0]
                            path_parts = domain_parts[1:]
                            fixed_url = f"{protocol}://{domain}/" + "/".join(path_parts)
                            urls.append(fixed_url)
                    else:
                        # 已经有路径分隔符
                        urls.append(line)
                else:
                    urls.append(line)
            elif line.startswith('http://') or line.startswith('https://'):
                urls.append(line)
            elif '.' in line and not line.startswith('http'):
                # 可能是缺少协议的URL
                urls.append('https://' + line)
        
        # 去重并过滤有效URL
        valid_urls = []
        for url in set(urls):
            try:
                parsed = urlparse(url)
                if parsed.netloc and parsed.scheme in ['http', 'https']:
                    valid_urls.append(url)
            except:
                continue
        
        return valid_urls

    def get_next_user_agent(self):
        """获取下一个User-Agent"""
        ua = self.user_agents[self.current_ua_index]
        self.current_ua_index = (self.current_ua_index + 1) % len(self.user_agents)
        return ua

    def extract_time_from_html(self, html_content, url):
        """从HTML内容中提取发布时间 - 优化版本"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 方法1: 尝试从特定的HTML元素中提取时间
            for selector in self.time_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        # 检查各种属性
                        for attr in ['datetime', 'content', 'data-date', 'data-time']:
                            if element.get(attr):
                                time_str = element.get(attr)
                                parsed_time = self.parse_time_string(time_str)
                                if parsed_time:
                                    return parsed_time
                        
                        # 检查元素文本内容
                        if element.text:
                            parsed_time = self.parse_time_string(element.text.strip())
                            if parsed_time:
                                return parsed_time
                except:
                    continue
            
            # 方法2: 检查JSON-LD结构化数据
            json_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_scripts:
                try:
                    if script.string:
                        data = json.loads(script.string)
                        if isinstance(data, dict):
                            for key in ['datePublished', 'publishedDate', 'dateCreated', 'dateModified']:
                                if key in data:
                                    parsed_time = self.parse_time_string(data[key])
                                    if parsed_time:
                                        return parsed_time
                        elif isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    for key in ['datePublished', 'publishedDate', 'dateCreated']:
                                        if key in item:
                                            parsed_time = self.parse_time_string(item[key])
                                            if parsed_time:
                                                return parsed_time
                except:
                    continue
            
            # 方法3: 在页面文本中搜索时间模式
            page_text = soup.get_text()
            for pattern in self.time_patterns:
                matches = re.findall(pattern, page_text)
                for match in matches:
                    parsed_time = self.parse_time_string(match)
                    if parsed_time:
                        # 验证时间是否合理（不能是未来时间，不能太久远）
                        try:
                            dt = datetime.strptime(parsed_time.split()[0], '%Y-%m-%d')
                            current_year = datetime.now().year
                            if 2000 <= dt.year <= current_year + 1:
                                return parsed_time
                        except:
                            continue
            
            # 方法4: 尝试从URL路径中提取日期
            url_date = self.extract_date_from_url(url)
            if url_date:
                return url_date
            
            return None
            
        except Exception as e:
            logger.error(f"解析HTML时出错 {url}: {str(e)}")
            return None

    def extract_date_from_url(self, url):
        """从URL路径中提取日期"""
        try:
            # 常见的URL日期格式
            url_patterns = [
                r'/(\d{4})/(\d{2})/(\d{2})/',
                r'/(\d{4})-(\d{2})-(\d{2})/',
                r'/(\d{4})(\d{2})(\d{2})/',
                r'(\d{4})/(\d{2})/(\d{2})',
                r'(\d{4})-(\d{2})-(\d{2})',
            ]
            
            for pattern in url_patterns:
                match = re.search(pattern, url)
                if match:
                    year, month, day = match.groups()
                    try:
                        dt = datetime(int(year), int(month), int(day))
                        if 2000 <= dt.year <= datetime.now().year + 1:
                            return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def parse_time_string(self, time_str):
        """解析时间字符串为标准格式 - 优化版本"""
        if not time_str:
            return None
        
        time_str = str(time_str).strip()
        
        # 清理时间字符串
        time_str = re.sub(r'[^\w\s:/-]', ' ', time_str)
        time_str = re.sub(r'\s+', ' ', time_str).strip()
        
        # 扩展的时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y.%m.%d',
            '%d.%m.%Y',
            '%d %B %Y',
            '%B %d %Y',
            '%d %b %Y',
            '%b %d %Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        
        # 尝试使用正则表达式提取时间部分
        for pattern in self.time_patterns:
            match = re.search(pattern, time_str)
            if match:
                matched_str = match.group()
                for fmt in formats:
                    try:
                        dt = datetime.strptime(matched_str, fmt)
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        continue
        
        return None

    def is_blocked_domain(self, url):
        """检查是否是被阻止的域名"""
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc
            return domain in self.blocked_domains
        except:
            return False

    def fetch_publish_time_from_url(self, url):
        """从单个URL获取发布时间 - 优化版本"""
        try:
            # 检查是否是被阻止的域名
            if self.is_blocked_domain(url):
                logger.warning(f"跳过已知被阻止的域名: {url}")
                return None

            logger.info(f"正在访问: {url}")

            # 轮换User-Agent
            headers = {
                'User-Agent': self.get_next_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            }

            response = self.session.get(url, timeout=self.timeout, headers=headers, verify=False)
            response.raise_for_status()

            # 尝试检测编码
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding

            publish_time = self.extract_time_from_html(response.text, url)

            if publish_time:
                logger.info(f"成功提取时间: {publish_time}")
                return publish_time
            else:
                logger.warning(f"未能从页面中提取到时间信息: {url}")
                return None

        except requests.exceptions.SSLError as e:
            logger.error(f"SSL错误 {url}: {str(e)}")
            return None
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时 {url}: {str(e)}")
            return None
        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误 {url}: {str(e)}")
            return None
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 403:
                logger.error(f"访问被拒绝(403) {url} - 添加到阻止列表")
                # 动态添加到阻止列表
                try:
                    domain = urlparse(url).netloc
                    self.blocked_domains.add(domain)
                except:
                    pass
            logger.error(f"HTTP错误 {url}: {str(e)}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败 {url}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"处理URL时出错 {url}: {str(e)}")
            return None

    def process_data_sources(self, data_sources_text):
        """处理data_sources列，提取发布时间 - 优化版本"""
        urls = self.fix_malformed_urls(data_sources_text)

        if not urls:
            logger.warning(f"无法从data_sources中提取有效URL: {data_sources_text}")
            return "无法获取"

        logger.info(f"提取到 {len(urls)} 个URL")

        # 过滤掉已知被阻止的URL
        valid_urls = [url for url in urls if not self.is_blocked_domain(url)]
        blocked_count = len(urls) - len(valid_urls)

        if blocked_count > 0:
            logger.info(f"跳过 {blocked_count} 个已知被阻止的URL")

        if not valid_urls:
            logger.warning("所有URL都在阻止列表中")
            return "无法获取"

        # 尝试从每个有效URL提取时间
        for i, url in enumerate(valid_urls):
            logger.info(f"尝试URL {i+1}/{len(valid_urls)}: {url}")

            publish_time = self.fetch_publish_time_from_url(url)
            if publish_time:
                return publish_time

            # 添加延迟避免过于频繁的请求
            time.sleep(self.delay)

        return "无法获取"

def process_failed_csv_file(file_path, extractor, output_folder):
    """处理失败记录的CSV文件"""
    logger.info(f"开始重新处理文件: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path, encoding='utf-8')
    
    if 'data_sources' not in df.columns or 'publish_time' not in df.columns:
        logger.error(f"文件 {file_path} 缺少必要的列")
        return 0, 0
    
    success_count = 0
    fail_count = 0
    
    # 处理每一行
    for index, row in df.iterrows():
        logger.info(f"重新处理第 {index + 1}/{len(df)} 行 (ID: {row.get('id', 'N/A')})")
        
        # 重新提取发布时间
        publish_time = extractor.process_data_sources(row['data_sources'])
        
        # 更新DataFrame
        df.at[index, 'publish_time'] = publish_time
        
        if publish_time != "无法获取":
            success_count += 1
            logger.info(f"第 {index + 1} 行成功提取时间: {publish_time}")
        else:
            fail_count += 1
            logger.warning(f"第 {index + 1} 行仍然无法提取时间")
    
    # 保存更新后的文件
    output_file = Path(output_folder) / f"retry_{Path(file_path).name}"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    logger.info(f"重新处理的文件已保存到: {output_file}")
    
    return success_count, fail_count

def main():
    """主函数"""
    failed_folder = "failed_records"
    output_folder = "retry_results"
    
    # 创建输出文件夹
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    csv_files = [
        "东海_extracted_failed.csv",
        "南海_extracted_failed.csv", 
        "黄渤海_extracted_failed.csv"
    ]
    
    extractor = OptimizedPublishTimeExtractor()
    
    total_success = 0
    total_fail = 0
    
    report = []
    
    logger.info("开始重新提取失败记录的发布时间...")
    logger.info("=" * 60)
    
    for csv_file in csv_files:
        file_path = Path(failed_folder) / csv_file
        
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            continue
        
        success, fail = process_failed_csv_file(file_path, extractor, output_folder)
        total_success += success
        total_fail += fail
        
        report.append({
            'file': csv_file,
            'success': success,
            'fail': fail,
            'total': success + fail
        })
        
        logger.info(f"{csv_file} 重新处理完成: 成功 {success} 条，失败 {fail} 条")
        logger.info("-" * 40)
    
    # 生成处理报告
    logger.info("=" * 60)
    logger.info("重新处理报告:")
    logger.info("=" * 60)
    
    for item in report:
        logger.info(f"文件: {item['file']}")
        logger.info(f"  总记录数: {item['total']}")
        logger.info(f"  成功提取: {item['success']}")
        logger.info(f"  仍然失败: {item['fail']}")
        if item['total'] > 0:
            success_rate = (item['success'] / item['total']) * 100
            logger.info(f"  成功率: {success_rate:.1f}%")
        logger.info("")
    
    logger.info(f"总计:")
    logger.info(f"  成功提取: {total_success} 条")
    logger.info(f"  仍然失败: {total_fail} 条")
    logger.info(f"  总记录数: {total_success + total_fail} 条")
    if (total_success + total_fail) > 0:
        overall_success_rate = (total_success / (total_success + total_fail)) * 100
        logger.info(f"  总成功率: {overall_success_rate:.1f}%")
    
    logger.info("=" * 60)
    logger.info("重新处理完成！")

if __name__ == "__main__":
    main()
