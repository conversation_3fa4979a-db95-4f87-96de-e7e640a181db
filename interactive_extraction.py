#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式时间提取脚本
启动浏览器窗口，分批处理链接，等待用户手动处理验证码后继续
"""

import pandas as pd
import re
from datetime import datetime
import time
import json
from pathlib import Path
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InteractiveExtractor:
    def __init__(self):
        self.driver = None
        self.setup_driver()
        
        # 时间模式
        self.time_patterns = [
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\w+\s+\d{1,2},\s+\d{4}',
            r'\d{1,2}\s+\w+\s+\d{4}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{2}\.\d{2}\.\d{4}',
        ]
        
        # 时间选择器
        self.time_selectors = [
            'time[datetime]', '[datetime]', 'time[pubdate]',
            'meta[property="article:published_time"]',
            'meta[property="article:modified_time"]',
            'meta[name="publishdate"]', 'meta[name="date"]',
            'meta[name="DC.date.issued"]', 'meta[name="DC.date.created"]',
            'meta[name="article:published_time"]',
            'meta[itemprop="datePublished"]',
            'meta[itemprop="dateCreated"]',
            '.publish-time', '.publication-date', '.article-date', 
            '.date', '.timestamp', '.entry-date', '.post-date',
            '.created-date', '.updated-date', '.byline',
            '.author-date', '.article-meta', '.news-date',
            '.story-date', '.content-date',
            '[class*="date"]', '[id*="date"]', 
            '[class*="time"]', '[id*="time"]',
            '[class*="publish"]', '[id*="publish"]',
            '[data-date]', '[data-time]', '[data-published]',
            '[data-timestamp]', '[data-publish-date]',
        ]

    def setup_driver(self):
        """设置Chrome浏览器（可见窗口）"""
        try:
            chrome_options = Options()
            # 不使用无头模式，让用户可以看到浏览器
            # chrome_options.add_argument('--headless')  # 注释掉无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-web-security')
            
            # 使用webdriver-manager自动管理Chrome驱动
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(5)
            
            logger.info("Chrome浏览器窗口已启动（可见模式）")
            
        except Exception as e:
            logger.error(f"无法初始化Chrome浏览器: {str(e)}")
            self.driver = None

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL并正确分割"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        # 处理多种分隔符
        separators = [';', '；', '\n', '|']
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # 修复格式错误的URL
            if re.match(r'^https?://[^/\s]+-', part):
                url_parts = part.split('://', 1)
                if len(url_parts) == 2:
                    protocol, rest = url_parts
                    if '/' not in rest and '-' in rest:
                        domain_parts = rest.split('-')
                        if len(domain_parts) > 1 and '.' in domain_parts[0]:
                            domain = domain_parts[0]
                            path_parts = domain_parts[1:]
                            fixed_url = f"{protocol}://{domain}/" + "/".join(path_parts)
                            urls.append(fixed_url)
                        else:
                            urls.append(part)
                    else:
                        urls.append(part)
                else:
                    urls.append(part)
            elif part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part and not part.startswith('http'):
                urls.append('https://' + part)
        
        return list(set(urls))

    def extract_date_from_url(self, url):
        """从URL路径中提取日期"""
        try:
            patterns = [
                r'/(\d{4})/(\d{2})/(\d{2})/',
                r'/(\d{4})-(\d{2})-(\d{2})/',
                r'/(\d{4})(\d{2})(\d{2})/',
                r'(\d{4})/(\d{2})/(\d{2})',
                r'(\d{4})-(\d{2})-(\d{2})',
                r'/(\d{4})/(\d{1,2})/(\d{1,2})/',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    groups = match.groups()
                    if len(groups) == 3:
                        year, month, day = groups
                    elif len(groups) == 1 and len(groups[0]) == 8:
                        date_str = groups[0]
                        year, month, day = date_str[:4], date_str[4:6], date_str[6:8]
                    else:
                        continue
                    
                    try:
                        month = month.zfill(2)
                        day = day.zfill(2)
                        dt = datetime(int(year), int(month), int(day))
                        if 2000 <= dt.year <= datetime.now().year + 1:
                            return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def parse_time_string(self, time_str):
        """解析时间字符串"""
        if not time_str:
            return None
        
        time_str = str(time_str).strip()
        time_str = re.sub(r'[^\w\s:/-]', ' ', time_str)
        time_str = re.sub(r'\s+', ' ', time_str).strip()
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y.%m.%d',
            '%d.%m.%Y',
            '%d %B %Y',
            '%B %d %Y',
            '%d %b %Y',
            '%b %d %Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        
        return None

    def extract_time_from_current_page(self, url):
        """从当前页面提取时间"""
        try:
            # 先尝试从URL提取日期
            url_date = self.extract_date_from_url(url)
            if url_date:
                logger.info(f"从URL提取时间: {url_date}")
                return url_date
            
            # 等待页面加载完成
            time.sleep(2)
            
            # 方法1: 尝试从特定元素提取时间
            for selector in self.time_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        # 检查各种属性
                        for attr in ['datetime', 'content', 'data-date', 'data-time', 'data-published', 'data-timestamp']:
                            try:
                                attr_value = element.get_attribute(attr)
                                if attr_value:
                                    parsed_time = self.parse_time_string(attr_value)
                                    if parsed_time:
                                        logger.info(f"从属性 {attr} 提取时间: {parsed_time}")
                                        return parsed_time
                            except:
                                continue
                        
                        # 检查元素文本
                        try:
                            text = element.text.strip()
                            if text:
                                parsed_time = self.parse_time_string(text)
                                if parsed_time:
                                    logger.info(f"从元素文本提取时间: {parsed_time}")
                                    return parsed_time
                        except:
                            continue
                except:
                    continue
            
            # 方法2: 检查页面源码中的JSON-LD
            try:
                scripts = self.driver.find_elements(By.CSS_SELECTOR, 'script[type="application/ld+json"]')
                for script in scripts:
                    try:
                        script_content = script.get_attribute('innerHTML')
                        if script_content:
                            data = json.loads(script_content)
                            if isinstance(data, dict):
                                for key in ['datePublished', 'publishedDate', 'dateCreated', 'dateModified']:
                                    if key in data:
                                        parsed_time = self.parse_time_string(data[key])
                                        if parsed_time:
                                            logger.info(f"从JSON-LD提取时间: {parsed_time}")
                                            return parsed_time
                    except:
                        continue
            except:
                pass
            
            # 方法3: 在页面文本中搜索时间模式
            try:
                page_source = self.driver.page_source
                for pattern in self.time_patterns:
                    matches = re.findall(pattern, page_source)
                    for match in matches:
                        parsed_time = self.parse_time_string(match)
                        if parsed_time:
                            # 验证时间合理性
                            try:
                                dt = datetime.strptime(parsed_time.split()[0], '%Y-%m-%d')
                                current_year = datetime.now().year
                                if 2000 <= dt.year <= current_year + 1:
                                    logger.info(f"从页面文本提取时间: {parsed_time}")
                                    return parsed_time
                            except:
                                continue
            except:
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"提取时间时出错 {url}: {str(e)}")
            return None

    def open_urls_in_tabs(self, urls):
        """在新标签页中打开多个URL"""
        if not self.driver:
            logger.error("浏览器未初始化")
            return False
        
        try:
            # 打开第一个URL
            if urls:
                logger.info(f"打开第一个URL: {urls[0]}")
                self.driver.get(urls[0])
                time.sleep(2)
            
            # 在新标签页中打开其余URL
            for i, url in enumerate(urls[1:], 1):
                logger.info(f"在新标签页打开URL {i+1}: {url}")
                self.driver.execute_script(f"window.open('{url}', '_blank');")
                time.sleep(1)  # 短暂延迟避免过快打开
            
            logger.info(f"已在 {len(urls)} 个标签页中打开所有URL")
            return True
            
        except Exception as e:
            logger.error(f"打开URL时出错: {str(e)}")
            return False

    def process_batch(self, batch_urls, batch_ids):
        """处理一批URL"""
        if not self.open_urls_in_tabs(batch_urls):
            return {}
        
        print(f"\n{'='*60}")
        print(f"已在浏览器中打开 {len(batch_urls)} 个链接")
        print("请手动处理任何验证码、机器人检测或登录要求")
        print("处理完成后，按回车键继续自动提取时间...")
        print(f"{'='*60}")
        
        # 等待用户确认
        input("按回车键继续...")
        
        results = {}
        
        # 获取所有标签页句柄
        all_handles = self.driver.window_handles
        
        # 遍历每个标签页提取时间
        for i, (url, record_id) in enumerate(zip(batch_urls, batch_ids)):
            try:
                if i < len(all_handles):
                    # 切换到对应标签页
                    self.driver.switch_to.window(all_handles[i])
                    current_url = self.driver.current_url
                    
                    logger.info(f"处理标签页 {i+1}: {record_id} - {current_url}")
                    
                    # 提取时间
                    publish_time = self.extract_time_from_current_page(url)
                    
                    if publish_time:
                        results[record_id] = publish_time
                        logger.info(f"✅ {record_id}: {publish_time}")
                    else:
                        results[record_id] = "无法获取"
                        logger.warning(f"❌ {record_id}: 无法获取时间")
                
            except Exception as e:
                logger.error(f"处理 {record_id} 时出错: {str(e)}")
                results[record_id] = "无法获取"
        
        # 关闭除第一个标签页外的所有标签页
        for handle in all_handles[1:]:
            try:
                self.driver.switch_to.window(handle)
                self.driver.close()
            except:
                pass
        
        # 切换回第一个标签页
        if all_handles:
            self.driver.switch_to.window(all_handles[0])
        
        return results

    def process_csv_file(self, file_path, output_folder, batch_size=20):
        """处理CSV文件，分批处理"""
        logger.info(f"开始交互式处理文件: {file_path}")
        
        df = pd.read_csv(file_path, encoding='utf-8')
        
        if 'data_sources' not in df.columns or 'publish_time' not in df.columns:
            logger.error(f"文件 {file_path} 缺少必要的列")
            return 0, 0
        
        success_count = 0
        fail_count = 0
        
        # 分批处理
        total_records = len(df)
        for start_idx in range(0, total_records, batch_size):
            end_idx = min(start_idx + batch_size, total_records)
            batch_df = df.iloc[start_idx:end_idx]
            
            print(f"\n处理批次: {start_idx + 1}-{end_idx} / {total_records}")
            
            # 准备批次数据
            batch_urls = []
            batch_ids = []
            batch_indices = []
            
            for idx, row in batch_df.iterrows():
                urls = self.fix_malformed_urls(row['data_sources'])
                if urls:
                    batch_urls.append(urls[0])  # 只取第一个URL
                    batch_ids.append(row.get('id', f'row_{idx}'))
                    batch_indices.append(idx)
            
            if not batch_urls:
                logger.warning(f"批次 {start_idx + 1}-{end_idx} 没有有效URL")
                continue
            
            # 处理批次
            results = self.process_batch(batch_urls, batch_ids)
            
            # 更新DataFrame
            for i, idx in enumerate(batch_indices):
                record_id = batch_ids[i]
                if record_id in results:
                    df.at[idx, 'publish_time'] = results[record_id]
                    if results[record_id] != "无法获取":
                        success_count += 1
                    else:
                        fail_count += 1
            
            # 询问是否继续
            if end_idx < total_records:
                continue_choice = input(f"\n已完成 {end_idx}/{total_records} 条记录。继续下一批次？(y/n): ")
                if continue_choice.lower() != 'y':
                    break
        
        # 保存结果
        output_file = Path(output_folder) / f"interactive_{Path(file_path).name}"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"结果已保存到: {output_file}")
        
        return success_count, fail_count

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                choice = input("\n是否关闭浏览器窗口？(y/n): ")
                if choice.lower() == 'y':
                    self.driver.quit()
                    logger.info("浏览器已关闭")
                else:
                    logger.info("浏览器窗口保持打开")
            except:
                pass

def main():
    """主函数"""
    failed_folder = "failed_records"
    output_folder = "interactive_results"
    
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    extractor = InteractiveExtractor()
    
    try:
        # 处理东海失败记录
        test_file = "东海_extracted_failed.csv"
        file_path = Path(failed_folder) / test_file
        
        if file_path.exists():
            print(f"\n开始交互式处理: {file_path}")
            print("每批次处理20个链接，您可以手动处理验证码后继续")
            
            success, fail = extractor.process_csv_file(file_path, output_folder, batch_size=20)
            
            print(f"\n{'='*60}")
            print(f"交互式处理完成:")
            print(f"成功: {success} 条")
            print(f"失败: {fail} 条")
            if success + fail > 0:
                success_rate = (success / (success + fail)) * 100
                print(f"成功率: {success_rate:.1f}%")
            print(f"{'='*60}")
        else:
            logger.error(f"文件不存在: {file_path}")
    
    finally:
        extractor.close()

if __name__ == "__main__":
    main()
