#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取CSV文件中的A、R、T列并合并成新的表格
A列: id
R列: data_sources  
T列: publish_time
"""

import pandas as pd
import os
from pathlib import Path

def extract_columns_from_csv(input_folder, output_folder):
    """
    从指定文件夹中的CSV文件提取A、R、T列，并保存到新文件夹
    
    Args:
        input_folder (str): 输入CSV文件夹路径
        output_folder (str): 输出文件夹路径
    """
    
    # 创建输出文件夹
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    # 要提取的列（按字母顺序，A=0, R=17, T=19）
    columns_to_extract = ['id', 'data_sources', 'publish_time']  # A, R, T列
    
    # 获取输入文件夹中的所有CSV文件
    input_path = Path(input_folder)
    csv_files = list(input_path.glob('*.csv'))
    
    if not csv_files:
        print(f"在 {input_folder} 文件夹中没有找到CSV文件")
        return
    
    # 用于存储所有数据的列表
    all_data = []
    
    # 处理每个CSV文件
    for csv_file in csv_files:
        print(f"正在处理文件: {csv_file.name}")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 检查是否包含所需的列
            missing_columns = [col for col in columns_to_extract if col not in df.columns]
            if missing_columns:
                print(f"警告: 文件 {csv_file.name} 缺少列: {missing_columns}")
                continue
            
            # 提取指定列
            extracted_data = df[columns_to_extract].copy()
            
            # 添加源文件信息
            extracted_data['source_file'] = csv_file.stem  # 文件名（不含扩展名）
            
            # 保存单个文件的提取结果
            output_file = Path(output_folder) / f"{csv_file.stem}_extracted.csv"
            extracted_data.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"已保存提取结果到: {output_file}")
            
            # 添加到总数据中
            all_data.append(extracted_data)
            
        except Exception as e:
            print(f"处理文件 {csv_file.name} 时出错: {str(e)}")
            continue
    
    # 合并所有数据
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 保存合并后的文件
        combined_file = Path(output_folder) / "combined_extracted.csv"
        combined_data.to_csv(combined_file, index=False, encoding='utf-8-sig')
        print(f"\n已保存合并文件到: {combined_file}")
        print(f"合并文件包含 {len(combined_data)} 行数据")
        
        # 显示统计信息
        print("\n各源文件数据统计:")
        source_counts = combined_data['source_file'].value_counts()
        for source, count in source_counts.items():
            print(f"  {source}: {count} 条记录")
            
    else:
        print("没有成功处理任何文件")

def main():
    """主函数"""
    # 设置输入和输出文件夹路径
    input_folder = "csv"  # 原始CSV文件夹
    output_folder = "extracted_data"  # 新的输出文件夹
    
    print("开始提取CSV文件中的A、R、T列...")
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件夹: {output_folder}")
    print("-" * 50)
    
    # 执行提取操作
    extract_columns_from_csv(input_folder, output_folder)
    
    print("-" * 50)
    print("提取操作完成!")

if __name__ == "__main__":
    main()
