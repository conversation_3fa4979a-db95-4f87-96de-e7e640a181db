#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对性时间提取脚本
基于失败原因分析，采用针对性解决方案
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import time
import json
from pathlib import Path
from urllib.parse import urlparse
import logging
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TargetedExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        
        # 多种User-Agent池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0',
        ]
        
        # 跳过的域名（根据分析报告）
        self.skip_domains = {
            'www.facebook.com', 'facebook.com',  # 需要登录
            'x.com', 'twitter.com',  # 需要登录，有验证码
            'flyteam.jp',  # 405错误
        }
        
        # 特殊处理的域名
        self.special_domains = {
            'www.mod.go.jp': self.extract_mod_go_jp,
            'def.ltn.com.tw': self.extract_ltn_tw,
            'www.pacom.mil': self.extract_pacom_mil,
            'www.pacaf.af.mil': self.extract_pacaf_mil,
            'www.navy.mil': self.extract_navy_mil,
            'www.usfj.mil': self.extract_usfj_mil,
        }

    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def should_skip_domain(self, url):
        """检查是否应该跳过该域名"""
        try:
            domain = urlparse(url).netloc.lower()
            return any(skip_domain in domain for skip_domain in self.skip_domains)
        except:
            return False

    def extract_date_from_url(self, url):
        """从URL路径中提取日期"""
        try:
            patterns = [
                r'/(\d{4})/(\d{2})/(\d{2})/',
                r'/(\d{4})-(\d{2})-(\d{2})/',
                r'/(\d{4})(\d{2})(\d{2})/',
                r'(\d{4})/(\d{2})/(\d{2})',
                r'(\d{4})-(\d{2})-(\d{2})',
                r'/(\d{4})/(\d{1,2})/(\d{1,2})/',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
                # 特殊格式：新闻ID中的日期
                r'(\d{4})(\d{2})(\d{2})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    groups = match.groups()
                    if len(groups) == 3:
                        year, month, day = groups
                    elif len(groups) == 1 and len(groups[0]) == 8:
                        # YYYYMMDD格式
                        date_str = groups[0]
                        year, month, day = date_str[:4], date_str[4:6], date_str[6:8]
                    else:
                        continue
                    
                    try:
                        month = month.zfill(2)
                        day = day.zfill(2)
                        dt = datetime(int(year), int(month), int(day))
                        if 2000 <= dt.year <= datetime.now().year + 1:
                            return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def extract_mod_go_jp(self, url, html_content):
        """专门处理mod.go.jp网站"""
        try:
            # 先从URL提取日期
            url_date = self.extract_date_from_url(url)
            if url_date:
                return url_date
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找日期元素
            date_elements = soup.find_all(['p', 'div', 'span'], string=re.compile(r'\d{4}\.\d{1,2}\.\d{1,2}'))
            for elem in date_elements:
                match = re.search(r'(\d{4})\.(\d{1,2})\.(\d{1,2})', elem.get_text())
                if match:
                    year, month, day = match.groups()
                    try:
                        dt = datetime(int(year), int(month), int(day))
                        return dt.strftime('%Y-%m-%d 00:00:00')
                    except:
                        continue
            
            return None
        except:
            return None

    def extract_ltn_tw(self, url, html_content):
        """专门处理def.ltn.com.tw网站"""
        try:
            # 从URL中的breakingnews ID提取日期
            match = re.search(r'/breakingnews/(\d+)', url)
            if match:
                news_id = match.group(1)
                # 尝试从新闻ID推断日期（通常前几位是日期相关）
                if len(news_id) >= 7:
                    # 假设格式类似4479670，可能包含日期信息
                    # 这需要根据实际网站规律调整
                    pass
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找时间元素
            time_elem = soup.find('time')
            if time_elem and time_elem.get('datetime'):
                return self.parse_time_string(time_elem.get('datetime'))
            
            # 查找包含日期的元素
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',
                r'\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}',
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, html_content)
                if match:
                    return self.parse_time_string(match.group())
            
            return None
        except:
            return None

    def extract_pacom_mil(self, url, html_content):
        """专门处理pacom.mil网站"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找发布日期
            date_elem = soup.find('div', class_='date')
            if date_elem:
                return self.parse_time_string(date_elem.get_text())
            
            # 查找meta标签
            meta_date = soup.find('meta', attrs={'name': 'DC.date.issued'})
            if meta_date and meta_date.get('content'):
                return self.parse_time_string(meta_date.get('content'))
            
            return None
        except:
            return None

    def extract_pacaf_mil(self, url, html_content):
        """专门处理pacaf.af.mil网站"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找日期元素
            date_elem = soup.find('span', class_='date')
            if date_elem:
                return self.parse_time_string(date_elem.get_text())
            
            # 查找包含日期的段落
            date_patterns = [r'(\w+\s+\d{1,2},\s+\d{4})']
            for pattern in date_patterns:
                match = re.search(pattern, html_content)
                if match:
                    return self.parse_time_string(match.group(1))
            
            return None
        except:
            return None

    def extract_navy_mil(self, url, html_content):
        """专门处理navy.mil网站"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找时间元素
            time_elem = soup.find('time')
            if time_elem and time_elem.get('datetime'):
                return self.parse_time_string(time_elem.get('datetime'))
            
            return None
        except:
            return None

    def extract_usfj_mil(self, url, html_content):
        """专门处理usfj.mil网站"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找发布日期
            date_elem = soup.find('div', class_='published')
            if date_elem:
                return self.parse_time_string(date_elem.get_text())
            
            return None
        except:
            return None

    def parse_time_string(self, time_str):
        """解析时间字符串"""
        if not time_str:
            return None
        
        time_str = str(time_str).strip()
        time_str = re.sub(r'[^\w\s:/-]', ' ', time_str)
        time_str = re.sub(r'\s+', ' ', time_str).strip()
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y.%m.%d',
            '%d.%m.%Y',
            '%d %B %Y',
            '%B %d %Y',
            '%d %b %Y',
            '%b %d %Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                continue
        
        return None

    def fetch_with_retry(self, url, max_retries=3):
        """带重试的请求"""
        for attempt in range(max_retries):
            try:
                headers = {
                    'User-Agent': self.get_random_user_agent(),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5,zh-CN,zh;q=0.3',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                }
                
                # 随机延迟
                if attempt > 0:
                    time.sleep(random.uniform(1, 3))
                
                response = self.session.get(url, timeout=15, headers=headers)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 403:
                    logger.warning(f"403错误，尝试 {attempt + 1}/{max_retries}: {url}")
                    continue
                else:
                    logger.warning(f"HTTP {response.status_code}，尝试 {attempt + 1}/{max_retries}: {url}")
                    continue
                    
            except Exception as e:
                logger.warning(f"请求异常，尝试 {attempt + 1}/{max_retries}: {url} - {str(e)}")
                continue
        
        return None

    def extract_time_from_url(self, url):
        """从URL提取时间"""
        if self.should_skip_domain(url):
            logger.info(f"跳过域名: {url}")
            return None
        
        # 先尝试从URL路径提取日期
        url_date = self.extract_date_from_url(url)
        if url_date:
            logger.info(f"从URL提取时间: {url_date}")
            return url_date
        
        logger.info(f"正在访问: {url}")
        
        response = self.fetch_with_retry(url)
        if not response:
            logger.warning(f"无法访问: {url}")
            return None
        
        try:
            # 检测编码
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding
            
            html_content = response.text
            domain = urlparse(url).netloc.lower()
            
            # 使用特殊处理器
            if domain in self.special_domains:
                result = self.special_domains[domain](url, html_content)
                if result:
                    logger.info(f"特殊处理器成功提取: {result}")
                    return result
            
            # 通用提取方法
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 方法1: 查找time元素
            time_elem = soup.find('time')
            if time_elem and time_elem.get('datetime'):
                result = self.parse_time_string(time_elem.get('datetime'))
                if result:
                    return result
            
            # 方法2: 查找meta标签
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                if meta.get('property') in ['article:published_time', 'article:modified_time']:
                    result = self.parse_time_string(meta.get('content'))
                    if result:
                        return result
                if meta.get('name') in ['publishdate', 'date', 'DC.date.issued']:
                    result = self.parse_time_string(meta.get('content'))
                    if result:
                        return result
            
            # 方法3: 在文本中搜索时间模式
            time_patterns = [
                r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}',
                r'\d{4}-\d{2}-\d{2}',
                r'\d{4}/\d{2}/\d{2}',
                r'\w+\s+\d{1,2},\s+\d{4}',
                r'\d{1,2}\s+\w+\s+\d{4}',
            ]
            
            for pattern in time_patterns:
                matches = re.findall(pattern, html_content)
                for match in matches:
                    result = self.parse_time_string(match)
                    if result:
                        # 验证时间合理性
                        try:
                            dt = datetime.strptime(result.split()[0], '%Y-%m-%d')
                            if 2000 <= dt.year <= datetime.now().year + 1:
                                return result
                        except:
                            continue
            
            return None
            
        except Exception as e:
            logger.error(f"处理内容时出错 {url}: {str(e)}")
            return None

    def fix_malformed_urls(self, data_sources_text):
        """修复格式错误的URL"""
        if pd.isna(data_sources_text) or not data_sources_text:
            return []
        
        text = str(data_sources_text)
        urls = []
        
        separators = [';', '；', '\n', '|']
        parts = [text]
        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            if part.startswith('http://') or part.startswith('https://'):
                urls.append(part)
            elif '.' in part and not part.startswith('http'):
                urls.append('https://' + part)
        
        return list(set(urls))

    def process_data_sources(self, data_sources_text):
        """处理data_sources列"""
        urls = self.fix_malformed_urls(data_sources_text)
        
        if not urls:
            return "无法获取"
        
        for url in urls:
            result = self.extract_time_from_url(url)
            if result:
                return result
            time.sleep(0.5)  # 短暂延迟
        
        return "无法获取"

def process_single_row_targeted(args):
    """针对性处理单行数据"""
    index, row = args
    
    extractor = TargetedExtractor()
    
    logger.info(f"处理第 {index + 1} 行 (ID: {row.get('id', 'N/A')})")
    
    publish_time = extractor.process_data_sources(row['data_sources'])
    
    return index, publish_time

def process_failed_csv_file_targeted(file_path, output_folder):
    """针对性处理失败记录"""
    logger.info(f"开始针对性处理文件: {file_path}")
    
    df = pd.read_csv(file_path, encoding='utf-8')
    
    if 'data_sources' not in df.columns or 'publish_time' not in df.columns:
        logger.error(f"文件 {file_path} 缺少必要的列")
        return 0, 0
    
    success_count = 0
    fail_count = 0
    
    logger.info(f"使用针对性方法处理，共 {len(df)} 条记录")
    
    args_list = [(index, row) for index, row in df.iterrows()]
    
    # 使用较少的并发以避免被封
    with ThreadPoolExecutor(max_workers=2) as executor:
        future_to_index = {executor.submit(process_single_row_targeted, args): args[0] for args in args_list}
        
        for future in as_completed(future_to_index):
            index, publish_time = future.result()
            
            df.at[index, 'publish_time'] = publish_time
            
            if publish_time != "无法获取":
                success_count += 1
                logger.info(f"第 {index + 1} 行成功提取时间: {publish_time}")
            else:
                fail_count += 1
                logger.warning(f"第 {index + 1} 行无法提取时间")
    
    # 保存结果
    output_file = Path(output_folder) / f"targeted_{Path(file_path).name}"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    logger.info(f"结果已保存到: {output_file}")
    
    return success_count, fail_count

def main():
    """主函数"""
    failed_folder = "failed_records"
    output_folder = "targeted_results"
    
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    # 处理东海失败记录
    test_file = "东海_extracted_failed.csv"
    file_path = Path(failed_folder) / test_file
    
    if file_path.exists():
        success, fail = process_failed_csv_file_targeted(file_path, output_folder)
        logger.info(f"针对性处理完成: 成功 {success} 条，失败 {fail} 条")
        if success + fail > 0:
            success_rate = (success / (success + fail)) * 100
            logger.info(f"成功率: {success_rate:.1f}%")
    else:
        logger.error(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
